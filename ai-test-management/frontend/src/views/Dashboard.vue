<template>
  <div class="dashboard-container">
    <div class="page-header">
      <h1 class="page-title">仪表盘</h1>
      <p class="page-subtitle">欢迎使用AI测试管理系统</p>
    </div>
    
    <!-- 概览卡片 -->
    <el-row :gutter="20" class="overview-cards">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon projects">
              <el-icon size="32"><FolderOpened /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-number">{{ stats?.overview.total_projects || 0 }}</div>
              <div class="card-label">项目总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon requirements">
              <el-icon size="32"><Document /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-number">{{ stats?.overview.total_requirements || 0 }}</div>
              <div class="card-label">需求总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon testcases">
              <el-icon size="32"><Files /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-number">{{ stats?.overview.total_testcases || 0 }}</div>
              <div class="card-label">用例总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon defects">
              <el-icon size="32"><Warning /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-number">{{ stats?.overview.total_defects || 0 }}</div>
              <div class="card-label">缺陷总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>测试用例状态分布</span>
          </template>
          <div class="chart-container">
            <v-chart :option="testcaseStatusOption" />
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>需求类别分布</span>
          </template>
          <div class="chart-container">
            <v-chart :option="requirementCategoryOption" />
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近项目 -->
    <el-card class="recent-projects">
      <template #header>
        <span>最近项目</span>
      </template>
      <el-table :data="stats?.recent_projects" style="width: 100%">
        <el-table-column prop="project_code" label="项目编号" width="120" />
        <el-table-column prop="name" label="项目名称" />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewProject(row.id)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import VChart from 'vue-echarts'
import 'echarts'
import { getDashboardStats } from '@/api/dashboard'
import type { DashboardStats } from '@/types/dashboard'

const router = useRouter()
const stats = ref<DashboardStats>()

const testcaseStatusOption = computed(() => ({
  tooltip: {
    trigger: 'item'
  },
  series: [
    {
      name: '测试用例状态',
      type: 'pie',
      radius: '50%',
      data: stats.value?.testcase_stats.by_status.map(item => ({
        value: item.count,
        name: item.status
      })) || []
    }
  ]
}))

const requirementCategoryOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  xAxis: {
    type: 'category',
    data: stats.value?.requirement_stats.by_category.map(item => item.category) || []
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '需求数量',
      type: 'bar',
      data: stats.value?.requirement_stats.by_category.map(item => item.count) || []
    }
  ]
}))

const fetchStats = async () => {
  try {
    stats.value = await getDashboardStats()
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const viewProject = (projectId: number) => {
  router.push(`/projects/${projectId}`)
}

onMounted(() => {
  fetchStats()
})
</script>

<style scoped>
.dashboard-container {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 32px;
  font-weight: 800;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  letter-spacing: -0.025em;
}

.page-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.overview-cards {
  margin-bottom: 32px;
}

.overview-card {
  height: 140px;
  border-radius: 16px;
  border: 1px solid var(--border-light);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
  background: var(--bg-color-light);
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.card-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-icon.projects {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.card-icon.requirements {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.card-icon.testcases {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.card-icon.defects {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.card-number {
  font-size: 32px;
  font-weight: 800;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.charts-section {
  margin-bottom: 32px;
}

.chart-card {
  height: 420px;
  border-radius: 16px;
  border: 1px solid var(--border-light);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  background: var(--bg-color-light);
}

.chart-card :deep(.el-card__header) {
  background-color: var(--bg-color-dark);
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
  color: var(--text-primary);
}

.chart-container {
  height: 340px;
  padding: 16px;
}

.recent-projects {
  margin-bottom: 32px;
  border-radius: 16px;
  border: 1px solid var(--border-light);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  background: var(--bg-color-light);
}

.recent-projects :deep(.el-card__header) {
  background-color: var(--bg-color-dark);
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
  color: var(--text-primary);
}

.recent-projects :deep(.el-table) {
  border-radius: 0;
  border: none;
}

.recent-projects :deep(.el-table th) {
  background-color: transparent;
  border-bottom: 1px solid var(--border-color);
}

.recent-projects :deep(.el-table td) {
  border-bottom: 1px solid var(--border-light);
}
</style>
