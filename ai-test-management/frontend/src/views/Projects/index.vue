<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">项目管理</h1>
      <p class="page-subtitle">管理和维护项目信息</p>
    </div>

    <!-- 工具栏 -->
    <div class="page-toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索项目名称、编号或描述"
          style="width: 300px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <div class="toolbar-right">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新建项目
        </el-button>
      </div>
    </div>
    
    <!-- 项目列表 -->
    <div class="content-card">
      <div class="card-header">
        <h3 class="card-title">项目列表</h3>
      </div>
      <div class="card-body">
        <div class="data-table">
          <el-table
            :data="projects"
            v-loading="loading"
            style="width: 100%"
          >
        <el-table-column prop="project_code" label="项目编号" width="120" />
        <el-table-column prop="name" label="项目名称" min-width="200" />
        <el-table-column prop="description" label="项目描述" min-width="300" show-overflow-tooltip />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewProject(row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="editProject(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteProject(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    
    <!-- 创建/编辑项目对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="projectFormRef"
        :model="projectForm"
        :rules="projectRules"
        label-width="100px"
      >
        <el-form-item label="项目编号" prop="project_code">
          <el-input v-model="projectForm.project_code" placeholder="请输入项目编号" />
        </el-form-item>
        
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="projectForm.name" placeholder="请输入项目名称" />
        </el-form-item>
        
        <el-form-item label="项目描述" prop="description">
          <el-input
            v-model="projectForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入项目描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { getProjects, createProject, updateProject, deleteProject as apiDeleteProject } from '@/api/projects'
import type { Project, ProjectCreate, ProjectUpdate } from '@/types/project'

const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const searchKeyword = ref('')
const projects = ref<Project[]>([])
const currentProject = ref<Project | null>(null)

const projectFormRef = ref<FormInstance>()
const projectForm = reactive<ProjectCreate>({
  project_code: '',
  name: '',
  description: ''
})

const projectRules: FormRules = {
  project_code: [
    { required: true, message: '请输入项目编号', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' }
  ]
}

const dialogTitle = computed(() => {
  return currentProject.value ? '编辑项目' : '新建项目'
})

const fetchProjects = async () => {
  loading.value = true
  try {
    projects.value = await getProjects({
      search: searchKeyword.value || undefined
    })
  } catch (error) {
    ElMessage.error('获取项目列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  fetchProjects()
}

const showCreateDialog = () => {
  currentProject.value = null
  resetForm()
  dialogVisible.value = true
}

const viewProject = (project: Project) => {
  // 跳转到项目详情页面
  console.log('查看项目:', project)
}

const editProject = (project: Project) => {
  currentProject.value = project
  projectForm.project_code = project.project_code
  projectForm.name = project.name
  projectForm.description = project.description || ''
  dialogVisible.value = true
}

const deleteProject = async (project: Project) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除项目 "${project.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await apiDeleteProject(project.id)
    ElMessage.success('删除成功')
    fetchProjects()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const submitForm = async () => {
  if (!projectFormRef.value) return
  
  await projectFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        if (currentProject.value) {
          // 编辑项目
          await updateProject(currentProject.value.id, projectForm as ProjectUpdate)
          ElMessage.success('更新成功')
        } else {
          // 创建项目
          await createProject(projectForm)
          ElMessage.success('创建成功')
        }
        
        dialogVisible.value = false
        fetchProjects()
      } catch (error: any) {
        ElMessage.error(error.response?.data?.detail || '操作失败')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

const resetForm = () => {
  projectForm.project_code = ''
  projectForm.name = ''
  projectForm.description = ''
  projectFormRef.value?.clearValidate()
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

onMounted(() => {
  fetchProjects()
})
</script>

<style scoped>
/* 页面样式已在全局样式中定义 */
</style>
