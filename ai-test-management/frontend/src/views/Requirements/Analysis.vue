<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">需求分析</h1>
      <p class="page-subtitle">使用AI智能分析需求文档</p>
    </div>

    <div class="content-card">
      <div class="card-header">
        <h3 class="card-title">AI需求分析</h3>
      </div>
      <div class="card-body">
        <el-form :model="analysisForm" label-width="120px">
        <el-form-item label="所属项目">
          <el-select v-model="analysisForm.project_id" placeholder="请选择项目" style="width: 100%">
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="需求标题">
          <el-input v-model="analysisForm.title" placeholder="请输入需求标题" />
        </el-form-item>
        
        <el-form-item label="需求内容">
          <el-input
            v-model="analysisForm.content"
            type="textarea"
            :rows="8"
            placeholder="请输入需求内容或上传需求文档"
          />
        </el-form-item>
        
        <el-form-item label="文档上传">
          <el-upload
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :before-upload="beforeUpload"
            accept=".txt,.docx"
            :limit="1"
          >
            <el-button type="primary">
              <el-icon><Upload /></el-icon>
              上传需求文档
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 .txt 和 .docx 格式，文件大小不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="startAnalysis" :loading="analyzing">
            <el-icon><Magic /></el-icon>
            AI分析需求
          </el-button>
          <el-button @click="saveRequirement" :disabled="!analysisResult">
            <el-icon><Check /></el-icon>
            保存需求
          </el-button>
        </el-form-item>
      </el-form>

      <!-- AI处理中的动画 -->
      <div v-if="analyzing" class="ai-processing">
        <div class="ai-animation">
          <img :src="IMAGES.AI_PROCESSING" alt="AI处理中" class="ai-icon">
        </div>
        <p class="processing-text">AI正在分析需求文档，请稍候...</p>
      </div>
        </el-form>
      </div>
    </div>
    
    <!-- AI分析结果 -->
    <div v-if="analysisResult" class="content-card">
      <div class="card-header">
        <h3 class="card-title">AI分析结果</h3>
      </div>
      <div class="card-body">
        <div class="analysis-result">
        <div class="result-section">
          <h3>细化前需求</h3>
          <div class="content-box">{{ analysisForm.content }}</div>
        </div>
        
        <div class="result-section">
          <h3>细化后需求</h3>
          <div class="content-box" v-html="analysisResult"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getProjects } from '@/api/projects'
import { useAuthStore } from '@/stores/auth'
import { IMAGES } from '@/utils/images'
import type { Project } from '@/types/project'

const authStore = useAuthStore()
const projects = ref<Project[]>([])
const analyzing = ref(false)
const analysisResult = ref('')

const analysisForm = reactive({
  project_id: null as number | null,
  title: '',
  content: ''
})

const uploadUrl = '/api/v1/requirements/upload'
const uploadHeaders = {
  Authorization: `Bearer ${authStore.token}`
}

const fetchProjects = async () => {
  try {
    projects.value = await getProjects()
  } catch (error) {
    ElMessage.error('获取项目列表失败')
  }
}

const beforeUpload = (file: File) => {
  const isValidType = file.type === 'text/plain' || 
    file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只支持 .txt 和 .docx 格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response: any) => {
  ElMessage.success('文件上传成功')
  // 这里可以读取文件内容并填充到需求内容中
  console.log('上传成功:', response)
}

const startAnalysis = async () => {
  if (!analysisForm.content.trim()) {
    ElMessage.warning('请输入需求内容')
    return
  }
  
  analyzing.value = true
  
  // 模拟AI分析过程
  setTimeout(() => {
    analysisResult.value = `
      <h4>需求分析结果</h4>
      <p><strong>功能需求：</strong></p>
      <ul>
        <li>用户登录功能</li>
        <li>数据展示功能</li>
        <li>权限管理功能</li>
      </ul>
      <p><strong>非功能需求：</strong></p>
      <ul>
        <li>系统响应时间不超过2秒</li>
        <li>支持1000并发用户</li>
        <li>数据安全性要求</li>
      </ul>
      <p><strong>验收标准：</strong></p>
      <ul>
        <li>用户能够成功登录系统</li>
        <li>数据能够正确显示</li>
        <li>权限控制有效</li>
      </ul>
    `
    analyzing.value = false
    ElMessage.success('AI分析完成')
  }, 3000)
}

const saveRequirement = () => {
  if (!analysisForm.project_id) {
    ElMessage.warning('请选择所属项目')
    return
  }
  
  // 保存需求到数据库
  ElMessage.success('需求保存成功')
}

onMounted(() => {
  fetchProjects()
})
</script>

<style scoped>

.analysis-result {
  display: flex;
  gap: 24px;
}

.result-section {
  flex: 1;
}

.result-section h3 {
  color: #303133;
  margin-bottom: 12px;
}

.content-box {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  min-height: 200px;
  white-space: pre-wrap;
}

.ai-processing {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  margin: 20px 0;
}

.ai-animation {
  margin-bottom: 16px;
}

.ai-icon {
  width: 120px;
  height: 120px;
  animation: pulse 2s infinite;
}

.processing-text {
  font-size: 16px;
  color: #606266;
  margin: 0;
  font-weight: 500;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
</style>
