// Element Plus 主题定制

// 覆盖 Element Plus 默认样式 - 匹配参考系统
:root {
  // 主色调 - 蓝色主题
  --el-color-primary: #1890ff;
  --el-color-primary-light-3: #40a9ff;
  --el-color-primary-light-5: #69c0ff;
  --el-color-primary-light-7: #91d5ff;
  --el-color-primary-light-8: #bae7ff;
  --el-color-primary-light-9: #e6f7ff;
  --el-color-primary-dark-2: #096dd9;

  // 成功色
  --el-color-success: #52c41a;
  --el-color-success-light-3: #73d13d;
  --el-color-success-light-5: #95de64;
  --el-color-success-light-7: #b7eb8f;
  --el-color-success-light-8: #d9f7be;
  --el-color-success-light-9: #f6ffed;

  // 警告色
  --el-color-warning: #faad14;
  --el-color-warning-light-3: #ffc53d;
  --el-color-warning-light-5: #ffd666;
  --el-color-warning-light-7: #ffe58f;
  --el-color-warning-light-8: #fff1b8;
  --el-color-warning-light-9: #fffbe6;

  // 危险色
  --el-color-danger: #ff4d4f;
  --el-color-danger-light-3: #ff7875;
  --el-color-danger-light-5: #ffa39e;
  --el-color-danger-light-7: #ffccc7;
  --el-color-danger-light-8: #ffe1e1;
  --el-color-danger-light-9: #fff2f0;

  // 信息色
  --el-color-info: #1890ff;
  --el-color-info-light-3: #40a9ff;
  --el-color-info-light-5: #69c0ff;
  --el-color-info-light-7: #91d5ff;
  --el-color-info-light-8: #bae7ff;
  --el-color-info-light-9: #e6f7ff;
  
  // 边框圆角
  --el-border-radius-base: 8px;
  --el-border-radius-small: 6px;
  --el-border-radius-round: 20px;
  --el-border-radius-circle: 100%;
  
  // 字体
  --el-font-size-extra-large: 20px;
  --el-font-size-large: 18px;
  --el-font-size-medium: 16px;
  --el-font-size-base: 14px;
  --el-font-size-small: 13px;
  --el-font-size-extra-small: 12px;
}

// 按钮样式
.el-button {
  border-radius: var(--el-border-radius-base);
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &.is-round {
    border-radius: var(--el-border-radius-round);
  }
}

.el-button--primary {
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
  border: none;
  
  &:hover {
    background: linear-gradient(135deg, var(--el-color-primary-dark-2) 0%, var(--el-color-primary) 100%);
  }
}

// 卡片样式
.el-card {
  border-radius: 12px;
  border: 1px solid var(--border-light);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
  
  &:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  .el-card__header {
    background-color: var(--bg-color-dark);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--text-primary);
  }
}

// 输入框样式
.el-input {
  .el-input__wrapper {
    border-radius: var(--el-border-radius-base);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease-in-out;
    
    &:hover {
      border-color: var(--el-color-primary);
    }
    
    &.is-focus {
      border-color: var(--el-color-primary);
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }
  }
}

// 选择器样式
.el-select {
  .el-input__wrapper {
    border-radius: var(--el-border-radius-base);
  }
}

// 表格样式
.el-table {
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  
  th.el-table__cell {
    background-color: var(--bg-color-dark);
    color: var(--text-primary);
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
  }
  
  td.el-table__cell {
    border-bottom: 1px solid var(--border-light);
  }
  
  .el-table__row:hover > td {
    background-color: var(--bg-color-dark);
  }
}

// 对话框样式
.el-dialog {
  border-radius: 12px;
  
  .el-dialog__header {
    background-color: var(--bg-color-dark);
    border-bottom: 1px solid var(--border-color);
    border-radius: 12px 12px 0 0;
    
    .el-dialog__title {
      font-weight: 600;
      color: var(--text-primary);
    }
  }
  
  .el-dialog__body {
    padding: 24px;
  }
  
  .el-dialog__footer {
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-color-light);
    border-radius: 0 0 12px 12px;
  }
}

// 消息提示样式
.el-message {
  border-radius: var(--el-border-radius-base);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

// 分页样式
.el-pagination {
  .el-pager li {
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.2s ease-in-out;
    
    &:hover {
      transform: translateY(-1px);
    }
    
    &.is-active {
      background-color: var(--el-color-primary);
      color: white;
    }
  }
  
  .btn-prev,
  .btn-next {
    border-radius: 6px;
    transition: all 0.2s ease-in-out;
    
    &:hover {
      transform: translateY(-1px);
    }
  }
}

// 标签样式
.el-tag {
  border-radius: var(--el-border-radius-round);
  font-weight: 500;
}

// 上传组件样式
.el-upload {
  .el-upload-dragger {
    border-radius: var(--el-border-radius-base);
    border: 2px dashed var(--border-color);
    transition: all 0.2s ease-in-out;
    
    &:hover {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }
  }
}

// 菜单样式
.el-menu {
  .el-menu-item {
    border-radius: 8px;
    margin: 4px 12px;
    transition: all 0.2s ease-in-out;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
    
    &.is-active {
      background-color: var(--el-color-primary);
      color: white;
      font-weight: 600;
    }
  }
  
  .el-sub-menu__title {
    border-radius: 8px;
    margin: 4px 12px;
    transition: all 0.2s ease-in-out;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

// 表单样式
.el-form {
  .el-form-item__label {
    font-weight: 500;
    color: var(--text-primary);
  }
}

// 加载样式
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
}

// 抽屉样式
.el-drawer {
  .el-drawer__header {
    background-color: var(--bg-color-dark);
    border-bottom: 1px solid var(--border-color);
    
    .el-drawer__title {
      font-weight: 600;
      color: var(--text-primary);
    }
  }
}

// 步骤条样式
.el-steps {
  .el-step__title {
    font-weight: 500;
  }
  
  .el-step.is-process .el-step__icon {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }
  
  .el-step.is-finish .el-step__icon {
    background-color: var(--el-color-success);
    border-color: var(--el-color-success);
  }
}
