// Element Plus 主题定制

// 覆盖 Element Plus 默认样式 - 护眼绿色主题
:root {
  // 主色调 - 护眼绿色主题
  --el-color-primary: #7cb342;
  --el-color-primary-light-3: #9ccc65;
  --el-color-primary-light-5: #aed581;
  --el-color-primary-light-7: #c5e1a5;
  --el-color-primary-light-8: #dcedc8;
  --el-color-primary-light-9: #f1f8e9;
  --el-color-primary-dark-2: #558b2f;

  // 成功色
  --el-color-success: #66bb6a;
  --el-color-success-light-3: #81c784;
  --el-color-success-light-5: #a5d6a7;
  --el-color-success-light-7: #c8e6c9;
  --el-color-success-light-8: #e8f5e8;
  --el-color-success-light-9: #f3e5f5;

  // 警告色
  --el-color-warning: #ffb74d;
  --el-color-warning-light-3: #ffcc02;
  --el-color-warning-light-5: #ffd54f;
  --el-color-warning-light-7: #ffe082;
  --el-color-warning-light-8: #ffecb3;
  --el-color-warning-light-9: #fff8e1;

  // 危险色
  --el-color-danger: #ef5350;
  --el-color-danger-light-3: #e57373;
  --el-color-danger-light-5: #ef9a9a;
  --el-color-danger-light-7: #ffcdd2;
  --el-color-danger-light-8: #ffebee;
  --el-color-danger-light-9: #fce4ec;

  // 信息色
  --el-color-info: #42a5f5;
  --el-color-info-light-3: #64b5f6;
  --el-color-info-light-5: #90caf9;
  --el-color-info-light-7: #bbdefb;
  --el-color-info-light-8: #e3f2fd;
  --el-color-info-light-9: #f3e5f5;
  
  // 边框圆角
  --el-border-radius-base: 8px;
  --el-border-radius-small: 6px;
  --el-border-radius-round: 20px;
  --el-border-radius-circle: 100%;
  
  // 字体
  --el-font-size-extra-large: 20px;
  --el-font-size-large: 18px;
  --el-font-size-medium: 16px;
  --el-font-size-base: 14px;
  --el-font-size-small: 13px;
  --el-font-size-extra-small: 12px;
}

// 按钮样式
.el-button {
  border-radius: var(--el-border-radius-base);
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &.is-round {
    border-radius: var(--el-border-radius-round);
  }
}

.el-button--primary {
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
  border: none;
  
  &:hover {
    background: linear-gradient(135deg, var(--el-color-primary-dark-2) 0%, var(--el-color-primary) 100%);
  }
}

// 卡片样式
.el-card {
  border-radius: 12px;
  border: 1px solid var(--border-light);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
  
  &:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  .el-card__header {
    background-color: var(--bg-color-dark);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--text-primary);
  }
}

// 输入框样式
.el-input {
  .el-input__wrapper {
    border-radius: var(--el-border-radius-base);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease-in-out;
    
    &:hover {
      border-color: var(--el-color-primary);
    }
    
    &.is-focus {
      border-color: var(--el-color-primary);
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }
  }
}

// 选择器样式
.el-select {
  .el-input__wrapper {
    border-radius: var(--el-border-radius-base);
  }
}

// 表格样式
.el-table {
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  
  th.el-table__cell {
    background-color: var(--bg-color-dark);
    color: var(--text-primary);
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
  }
  
  td.el-table__cell {
    border-bottom: 1px solid var(--border-light);
  }
  
  .el-table__row:hover > td {
    background-color: var(--bg-color-dark);
  }
}

// 对话框样式
.el-dialog {
  border-radius: 12px;
  
  .el-dialog__header {
    background-color: var(--bg-color-dark);
    border-bottom: 1px solid var(--border-color);
    border-radius: 12px 12px 0 0;
    
    .el-dialog__title {
      font-weight: 600;
      color: var(--text-primary);
    }
  }
  
  .el-dialog__body {
    padding: 24px;
  }
  
  .el-dialog__footer {
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-color-light);
    border-radius: 0 0 12px 12px;
  }
}

// 消息提示样式
.el-message {
  border-radius: var(--el-border-radius-base);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

// 分页样式
.el-pagination {
  .el-pager li {
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.2s ease-in-out;
    
    &:hover {
      transform: translateY(-1px);
    }
    
    &.is-active {
      background-color: var(--el-color-primary);
      color: white;
    }
  }
  
  .btn-prev,
  .btn-next {
    border-radius: 6px;
    transition: all 0.2s ease-in-out;
    
    &:hover {
      transform: translateY(-1px);
    }
  }
}

// 标签样式
.el-tag {
  border-radius: var(--el-border-radius-round);
  font-weight: 500;
}

// 上传组件样式
.el-upload {
  .el-upload-dragger {
    border-radius: var(--el-border-radius-base);
    border: 2px dashed var(--border-color);
    transition: all 0.2s ease-in-out;
    
    &:hover {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }
  }
}

// 菜单样式
.el-menu {
  .el-menu-item {
    border-radius: 8px;
    margin: 4px 12px;
    transition: all 0.2s ease-in-out;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
    
    &.is-active {
      background-color: var(--el-color-primary);
      color: white;
      font-weight: 600;
    }
  }
  
  .el-sub-menu__title {
    border-radius: 8px;
    margin: 4px 12px;
    transition: all 0.2s ease-in-out;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

// 表单样式
.el-form {
  .el-form-item__label {
    font-weight: 500;
    color: var(--text-primary);
  }
}

// 加载样式
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
}

// 抽屉样式
.el-drawer {
  .el-drawer__header {
    background-color: var(--bg-color-dark);
    border-bottom: 1px solid var(--border-color);
    
    .el-drawer__title {
      font-weight: 600;
      color: var(--text-primary);
    }
  }
}

// 步骤条样式
.el-steps {
  .el-step__title {
    font-weight: 500;
  }
  
  .el-step.is-process .el-step__icon {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }
  
  .el-step.is-finish .el-step__icon {
    background-color: var(--el-color-success);
    border-color: var(--el-color-success);
  }
}
