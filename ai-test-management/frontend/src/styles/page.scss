// 通用页面样式

// 页面容器
.page-container {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f2f8ea 0%, #fefffe 100%);
}

// 页面头部
.page-header {
  margin-bottom: 32px;
  
  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 8px 0;
    letter-spacing: -0.025em;
  }
  
  .page-subtitle {
    font-size: 16px;
    color: var(--text-secondary);
    margin: 0;
  }
}

// 工具栏
.page-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 20px;
  background: var(--bg-color-light);
  border-radius: 12px;
  border: 1px solid var(--border-light);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  
  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

// 内容卡片
.content-card {
  background: var(--bg-color-light);
  border-radius: 12px;
  border: 1px solid var(--border-light);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  margin-bottom: 24px;
  overflow: hidden;
  
  .card-header {
    padding: 20px 24px;
    background: var(--bg-color-dark);
    border-bottom: 1px solid var(--border-color);
    
    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0;
    }
  }
  
  .card-body {
    padding: 24px;
  }
}

// 表单样式
.form-card {
  background: var(--bg-color-light);
  border-radius: 12px;
  border: 1px solid var(--border-light);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 24px;
  margin-bottom: 24px;
}

// 按钮组
.button-group {
  display: flex;
  gap: 12px;
  align-items: center;
  
  .el-button {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
  }
  
  .el-button--primary {
    background-color: #7cb342;
    border-color: #7cb342;
  }

  .el-button--primary:hover {
    background-color: #558b2f;
    border-color: #558b2f;
    transform: translateY(-1px);
  }
}

// 搜索框
.search-input {
  .el-input__wrapper {
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease-in-out;
  }
  
  .el-input__wrapper:hover {
    border-color: #7cb342;
  }

  .el-input__wrapper.is-focus {
    border-color: #7cb342;
    box-shadow: 0 0 0 3px rgba(124, 179, 66, 0.1);
  }
}

// 表格样式
.data-table {
  .el-table {
    background: var(--bg-color-light);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-color);
  }
  
  .el-table th {
    background: var(--bg-color-dark);
    color: var(--text-primary);
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
  }
  
  .el-table td {
    border-bottom: 1px solid var(--border-light);
  }
  
  .el-table tr:hover > td {
    background-color: var(--bg-color-dark);
  }
}

// 分页样式
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  
  .el-pagination {
    .el-pager li {
      border-radius: 6px;
      margin: 0 2px;
    }
    
    .el-pager li.is-active {
      background-color: #7cb342;
      color: white;
    }
  }
}

// 状态标签
.status-tag {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  
  &.success {
    background-color: rgba(102, 187, 106, 0.1);
    color: #66bb6a;
  }

  &.warning {
    background-color: rgba(255, 183, 77, 0.1);
    color: #ffb74d;
  }

  &.danger {
    background-color: rgba(239, 83, 80, 0.1);
    color: #ef5350;
  }

  &.info {
    background-color: rgba(66, 165, 245, 0.1);
    color: #42a5f5;
  }
}

// 响应式
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .page-toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .button-group {
    flex-wrap: wrap;
  }
}
