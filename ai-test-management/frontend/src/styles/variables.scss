// SCSS变量文件

// 颜色变量 - 匹配参考系统
$primary-color: #1890ff;
$primary-light: #40a9ff;
$primary-dark: #096dd9;
$success-color: #52c41a;
$warning-color: #faad14;
$danger-color: #ff4d4f;
$info-color: #1890ff;

// 背景颜色 - 现代蓝色主题
$bg-color: #f0f2f5;
$bg-color-light: #fafafa;
$bg-color-dark: #e6f7ff;

// 文字颜色 - 现代主题
$text-color-primary: #262626;
$text-color-regular: #595959;
$text-color-secondary: #8c8c8c;
$text-color-placeholder: #bfbfbf;

// 边框颜色 - 现代主题
$border-color-base: #d9d9d9;
$border-color-light: #e8e8e8;
$border-color-lighter: #f0f0f0;
$border-color-extra-light: #fafafa;

// 尺寸变量
$header-height: 60px;
$sidebar-width: 200px;
$sidebar-collapsed-width: 64px;

// 间距变量
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 圆角变量
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-large: 8px;

// 阴影变量
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
