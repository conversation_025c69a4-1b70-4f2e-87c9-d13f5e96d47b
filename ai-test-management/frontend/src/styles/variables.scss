// SCSS变量文件

// 颜色变量 - 以#f2f8ea为主的护眼配色
$primary-color: #7cb342;
$primary-light: #9ccc65;
$primary-dark: #558b2f;
$success-color: #66bb6a;
$warning-color: #ffb74d;
$danger-color: #ef5350;
$info-color: #42a5f5;

// 背景颜色 - 护眼绿色主题
$bg-color: #f2f8ea;
$bg-color-light: #fefffe;
$bg-color-dark: #e8f5e8;

// 文字颜色 - 护眼主题
$text-color-primary: #2e3d2e;
$text-color-regular: #4a5d4a;
$text-color-secondary: #6b7d6b;
$text-color-placeholder: #9eb09e;

// 边框颜色 - 护眼主题
$border-color-base: #c8d6c8;
$border-color-light: #d4e2d4;
$border-color-lighter: #e0ede0;
$border-color-extra-light: #ecf6ec;

// 尺寸变量
$header-height: 60px;
$sidebar-width: 200px;
$sidebar-collapsed-width: 64px;

// 间距变量
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 圆角变量
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-large: 8px;

// 阴影变量
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
