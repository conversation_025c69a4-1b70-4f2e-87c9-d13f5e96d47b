// 全局样式文件
@use './variables.scss' as vars;
@import './page.scss';
@import './element-plus.scss';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 现代化主题色
:root {
  --primary-color: #6366f1;
  --primary-light: #8b5cf6;
  --primary-dark: #4f46e5;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #6b7280;

  // 现代化背景色
  --bg-color: #f8fafc;
  --bg-color-light: #ffffff;
  --bg-color-dark: #f1f5f9;
  --bg-sidebar: #1e293b;
  --bg-header: #ffffff;

  // 文字颜色
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-light: #94a3b8;

  // 边框颜色
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
}

// 现代化布局样式
.layout-container {
  height: 100vh;
  background-color: var(--bg-color);
}

.main-content {
  background-color: var(--bg-color);
  min-height: calc(100vh - 64px);
  padding: 24px;
  overflow-y: auto;
}

// 现代化卡片样式
.custom-card {
  background-color: var(--bg-color-light);
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-light);
  margin-bottom: 24px;
  transition: all 0.2s ease-in-out;
}

.custom-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

// 现代化按钮样式
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-1px);
}

// 现代化表格样式
.el-table {
  background-color: var(--bg-color-light);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.el-table th {
  background-color: var(--bg-color-dark);
  color: var(--text-primary);
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
}

.el-table td {
  border-bottom: 1px solid var(--border-light);
}

// 表单样式
.form-container {
  background-color: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 工具栏样式
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 响应式
@media (max-width: 768px) {
  .main-content {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 10px;
  }
}
