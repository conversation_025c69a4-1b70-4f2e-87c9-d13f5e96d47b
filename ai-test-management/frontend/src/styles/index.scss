// 全局样式文件
@use './variables.scss' as vars;
@use './page.scss';
@use './element-plus.scss';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 护眼绿色主题 - 以#f2f8ea为主色
:root {
  --primary-color: #7cb342;
  --primary-light: #9ccc65;
  --primary-dark: #558b2f;
  --success-color: #66bb6a;
  --warning-color: #ffb74d;
  --danger-color: #ef5350;
  --info-color: #42a5f5;

  // 护眼背景色
  --bg-color: #f2f8ea;
  --bg-color-light: #fefffe;
  --bg-color-dark: #e8f5e8;
  --bg-sidebar: #2e4a2e;
  --bg-header: #fefffe;

  // 文字颜色
  --text-primary: #2e3d2e;
  --text-secondary: #4a5d4a;
  --text-light: #6b7d6b;

  // 边框颜色
  --border-color: #c8d6c8;
  --border-light: #e0ede0;
}

// 现代化布局样式
.layout-container {
  height: 100vh;
  background-color: var(--bg-color);
}

.main-content {
  background-color: var(--bg-color);
  min-height: calc(100vh - 64px);
  padding: 24px;
  overflow-y: auto;
}

// 现代化卡片样式
.custom-card {
  background-color: var(--bg-color-light);
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-light);
  margin-bottom: 24px;
  transition: all 0.2s ease-in-out;
}

.custom-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

// 现代化按钮样式
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-1px);
}

// 现代化表格样式
.el-table {
  background-color: var(--bg-color-light);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.el-table th {
  background-color: var(--bg-color-dark);
  color: var(--text-primary);
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
}

.el-table td {
  border-bottom: 1px solid var(--border-light);
}

// 表单样式
.form-container {
  background-color: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 工具栏样式
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 响应式
@media (max-width: 768px) {
  .main-content {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 10px;
  }
}
