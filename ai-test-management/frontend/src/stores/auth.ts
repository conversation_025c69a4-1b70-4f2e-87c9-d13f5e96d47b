import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login as apiLogin, getCurrentUser } from '@/api/auth'
import type { LoginForm, User } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(JSON.parse(localStorage.getItem('user') || 'null'))

  const isAuthenticated = computed(() => !!user.value)

  const login = async (loginForm: LoginForm) => {
    try {
      const response = await apiLogin(loginForm)

      // 直接设置用户信息，无需token
      user.value = response.user
      localStorage.setItem('user', JSON.stringify(response.user))

      return response
    } catch (error) {
      throw error
    }
  }

  const logout = () => {
    user.value = null
    localStorage.removeItem('user')
  }

  const fetchUserInfo = async () => {
    try {
      const userInfo = await getCurrentUser()
      user.value = userInfo
      localStorage.setItem('user', JSON.stringify(userInfo))
      return userInfo
    } catch (error) {
      logout()
      throw error
    }
  }

  const initAuth = async () => {
    const savedUser = localStorage.getItem('user')

    if (savedUser) {
      try {
        user.value = JSON.parse(savedUser)
        // 验证用户信息是否仍然有效
        await fetchUserInfo()
      } catch (error) {
        logout()
      }
    }
  }

  return {
    user,
    isAuthenticated,
    login,
    logout,
    fetchUserInfo,
    initAuth
  }
})
