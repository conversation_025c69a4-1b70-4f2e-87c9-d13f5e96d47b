// 认证相关类型定义

export interface LoginForm {
  username: string
  password: string
}

export interface LoginResponse {
  message: string
  user: User
}

export interface User {
  id: number
  username: string
  email: string
  full_name?: string
  is_active: boolean
  is_superuser: boolean
  created_at: string
  updated_at?: string
}

export interface UserCreate {
  username: string
  email: string
  password: string
  full_name?: string
  is_active?: boolean
  is_superuser?: boolean
}

export interface UserUpdate {
  username?: string
  email?: string
  password?: string
  full_name?: string
  is_active?: boolean
  is_superuser?: boolean
}
