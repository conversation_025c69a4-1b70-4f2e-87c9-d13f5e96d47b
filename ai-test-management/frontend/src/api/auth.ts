import request from './request'
import type { <PERSON>ginForm, LoginResponse, User } from '@/types/auth'

// 用户登录
export const login = (data: LoginForm): Promise<LoginResponse> => {
  const formData = new FormData()
  formData.append('username', data.username)
  formData.append('password', data.password)
  
  return request({
    url: '/api/v1/auth/login',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 获取当前用户信息
export const getCurrentUser = (): Promise<User> => {
  return request({
    url: '/api/v1/auth/me',
    method: 'get'
  })
}
