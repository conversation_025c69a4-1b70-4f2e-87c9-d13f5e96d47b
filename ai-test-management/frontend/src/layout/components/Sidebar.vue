<template>
  <div class="sidebar-container">
    <div class="logo-container">
      <div class="logo" v-if="!appStore.sidebarCollapsed">
        <svg viewBox="0 0 200 40" class="logo-svg">
          <defs>
            <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style="stop-color:#7cb342;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#9ccc65;stop-opacity:1" />
            </linearGradient>
          </defs>
          <text x="10" y="28" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="url(#logoGradient)">
            AI测试管理
          </text>
        </svg>
      </div>
      <div class="logo-mini" v-else>
        <svg viewBox="0 0 32 32" class="logo-mini-svg">
          <defs>
            <linearGradient id="logoMiniGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#7cb342;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#9ccc65;stop-opacity:1" />
            </linearGradient>
          </defs>
          <circle cx="16" cy="16" r="14" fill="url(#logoMiniGradient)"/>
          <text x="16" y="20" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white" text-anchor="middle">
            AI
          </text>
        </svg>
      </div>
    </div>
    
    <el-menu
      :default-active="activeMenu"
      :collapse="appStore.sidebarCollapsed"
      :unique-opened="true"
      router
      background-color="#2e4a2e"
      text-color="#9eb09e"
      active-text-color="#7cb342"
      class="sidebar-menu"
    >
      <template v-for="route in menuRoutes" :key="route.path">
        <el-sub-menu
          v-if="route.children && route.children.length > 0"
          :index="route.path"
        >
          <template #title>
            <el-icon v-if="route.meta?.icon">
              <component :is="route.meta.icon" />
            </el-icon>
            <span>{{ route.meta?.title }}</span>
          </template>
          
          <el-menu-item
            v-for="child in route.children"
            :key="child.path"
            :index="child.path"
          >
            <el-icon v-if="child.meta?.icon">
              <component :is="child.meta.icon" />
            </el-icon>
            <template #title>{{ child.meta?.title }}</template>
          </el-menu-item>
        </el-sub-menu>
        
        <el-menu-item v-else :index="route.path">
          <el-icon v-if="route.meta?.icon">
            <component :is="route.meta.icon" />
          </el-icon>
          <template #title>{{ route.meta?.title }}</template>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()

const activeMenu = computed(() => {
  return route.path
})

const menuRoutes = computed(() => {
  // 从路由配置中获取需要显示在菜单中的路由
  const layoutRoute = router.getRoutes().find(r => r.name === 'Layout')
  return layoutRoute?.children?.filter(child => child.meta?.title) || []
})
</script>

<style scoped>
.sidebar-container {
  height: 100vh;
  overflow-y: auto;
  background-color: #2e4a2e;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 64px;
  background-color: #1a2e1a;
  border-bottom: 1px solid #7cb342;
}

.logo {
  height: 36px;
  width: auto;
}

.logo-svg {
  height: 36px;
  width: auto;
}

.logo-mini {
  height: 36px;
  width: 36px;
}

.logo-mini-svg {
  height: 36px;
  width: 36px;
}

.sidebar-menu {
  border-right: none;
  height: calc(100vh - 64px);
}

.sidebar-menu:not(.el-menu--collapse) {
  width: 240px;
}

/* 自定义菜单项样式 */
:deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
  margin: 4px 12px;
  border-radius: 8px;
  transition: all 0.2s ease-in-out;
}

:deep(.el-menu-item:hover) {
  background-color: #3d5a3d !important;
  color: #ffffff !important;
}

:deep(.el-menu-item.is-active) {
  background-color: #7cb342 !important;
  color: #ffffff !important;
  font-weight: 600;
}

:deep(.el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
  margin: 4px 12px;
  border-radius: 8px;
  transition: all 0.2s ease-in-out;
}

:deep(.el-sub-menu__title:hover) {
  background-color: #3d5a3d !important;
  color: #ffffff !important;
}

:deep(.el-menu-item .el-icon) {
  margin-right: 8px;
  font-size: 18px;
}
</style>
