from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...controllers.testcase import testcase_controller
from ...schemas.testcase import TestCase, TestCaseCreate, TestCaseUpdate

router = APIRouter()


@router.get("/", response_model=List[TestCase])
def read_testcases(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None, description="搜索关键词"),
    project_id: Optional[int] = Query(None, description="项目ID"),
    requirement_id: Optional[int] = Query(None, description="需求ID"),
) -> Any:
    """获取测试用例列表"""
    testcases = testcase_controller.get_multi_with_search(
        db, skip=skip, limit=limit, search=search, 
        project_id=project_id, requirement_id=requirement_id
    )
    return testcases


@router.post("/", response_model=TestCase)
def create_testcase(
    *,
    db: Session = Depends(get_db),
    testcase_in: TestCaseCreate,

) -> Any:
    """创建测试用例"""
    testcase = testcase_controller.create_with_steps(db, obj_in=testcase_in)
    return testcase


@router.get("/{testcase_id}", response_model=TestCase)
def read_testcase(
    *,
    db: Session = Depends(get_db),
    testcase_id: int,
) -> Any:
    """获取测试用例详情"""
    testcase = testcase_controller.get(db, id=testcase_id)
    if not testcase:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="测试用例不存在"
        )
    return testcase


@router.put("/{testcase_id}", response_model=TestCase)
def update_testcase(
    *,
    db: Session = Depends(get_db),
    testcase_id: int,
    testcase_in: TestCaseUpdate,
) -> Any:
    """更新测试用例"""
    testcase = testcase_controller.get(db, id=testcase_id)
    if not testcase:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="测试用例不存在"
        )
    
    testcase = testcase_controller.update(db, db_obj=testcase, obj_in=testcase_in)
    return testcase


@router.delete("/{testcase_id}")
def delete_testcase(
    *,
    db: Session = Depends(get_db),
    testcase_id: int,
) -> Any:
    """删除测试用例"""
    testcase = testcase_controller.get(db, id=testcase_id)
    if not testcase:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="测试用例不存在"
        )
    
    testcase_controller.remove(db, id=testcase_id)
    return {"message": "测试用例删除成功"}


@router.get("/by-requirement/{requirement_id}", response_model=List[TestCase])
def read_testcases_by_requirement(
    *,
    db: Session = Depends(get_db),
    requirement_id: int,
) -> Any:
    """根据需求ID获取测试用例"""
    testcases = testcase_controller.get_by_requirement(db, requirement_id=requirement_id)
    return testcases
