<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="67">
            <item index="0" class="java.lang.String" itemvalue="pydantic" />
            <item index="1" class="java.lang.String" itemvalue="pydantic_core" />
            <item index="2" class="java.lang.String" itemvalue="websockets" />
            <item index="3" class="java.lang.String" itemvalue="httpx" />
            <item index="4" class="java.lang.String" itemvalue="pathspec" />
            <item index="5" class="java.lang.String" itemvalue="PyJWT" />
            <item index="6" class="java.lang.String" itemvalue="cffi" />
            <item index="7" class="java.lang.String" itemvalue="aerich" />
            <item index="8" class="java.lang.String" itemvalue="setuptools" />
            <item index="9" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="10" class="java.lang.String" itemvalue="Jinja2" />
            <item index="11" class="java.lang.String" itemvalue="httptools" />
            <item index="12" class="java.lang.String" itemvalue="pypika-tortoise" />
            <item index="13" class="java.lang.String" itemvalue="tomlkit" />
            <item index="14" class="java.lang.String" itemvalue="tortoise-orm" />
            <item index="15" class="java.lang.String" itemvalue="Pygments" />
            <item index="16" class="java.lang.String" itemvalue="aiosqlite" />
            <item index="17" class="java.lang.String" itemvalue="starlette" />
            <item index="18" class="java.lang.String" itemvalue="certifi" />
            <item index="19" class="java.lang.String" itemvalue="anyio" />
            <item index="20" class="java.lang.String" itemvalue="uvicorn" />
            <item index="21" class="java.lang.String" itemvalue="uvloop" />
            <item index="22" class="java.lang.String" itemvalue="passlib" />
            <item index="23" class="java.lang.String" itemvalue="annotated-types" />
            <item index="24" class="java.lang.String" itemvalue="pydantic-settings" />
            <item index="25" class="java.lang.String" itemvalue="watchfiles" />
            <item index="26" class="java.lang.String" itemvalue="dnspython" />
            <item index="27" class="java.lang.String" itemvalue="fastapi-cli" />
            <item index="28" class="java.lang.String" itemvalue="typer" />
            <item index="29" class="java.lang.String" itemvalue="iso8601" />
            <item index="30" class="java.lang.String" itemvalue="black" />
            <item index="31" class="java.lang.String" itemvalue="tzdata" />
            <item index="32" class="java.lang.String" itemvalue="orjson" />
            <item index="33" class="java.lang.String" itemvalue="rich" />
            <item index="34" class="java.lang.String" itemvalue="packaging" />
            <item index="35" class="java.lang.String" itemvalue="python-multipart" />
            <item index="36" class="java.lang.String" itemvalue="loguru" />
            <item index="37" class="java.lang.String" itemvalue="click" />
            <item index="38" class="java.lang.String" itemvalue="dictdiffer" />
            <item index="39" class="java.lang.String" itemvalue="fastapi" />
            <item index="40" class="java.lang.String" itemvalue="platformdirs" />
            <item index="41" class="java.lang.String" itemvalue="ruff" />
            <item index="42" class="java.lang.String" itemvalue="email_validator" />
            <item index="43" class="java.lang.String" itemvalue="isort" />
            <item index="44" class="java.lang.String" itemvalue="pytz" />
            <item index="45" class="java.lang.String" itemvalue="httpcore" />
            <item index="46" class="java.lang.String" itemvalue="idna" />
            <item index="47" class="java.lang.String" itemvalue="ujson" />
            <item index="48" class="java.lang.String" itemvalue="openai" />
            <item index="49" class="java.lang.String" itemvalue="pip" />
            <item index="50" class="java.lang.String" itemvalue="aiofiles" />
            <item index="51" class="java.lang.String" itemvalue="autogen-agentchat" />
            <item index="52" class="java.lang.String" itemvalue="autogen-core" />
            <item index="53" class="java.lang.String" itemvalue="python-docx" />
            <item index="54" class="java.lang.String" itemvalue="alembic" />
            <item index="55" class="java.lang.String" itemvalue="psycopg2-binary" />
            <item index="56" class="java.lang.String" itemvalue="autogen-ext" />
            <item index="57" class="java.lang.String" itemvalue="llama-index" />
            <item index="58" class="java.lang.String" itemvalue="python-jose" />
            <item index="59" class="java.lang.String" itemvalue="sqlalchemy" />
            <item index="60" class="java.lang.String" itemvalue="magentic" />
            <item index="61" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="62" class="java.lang.String" itemvalue="ecdsa" />
            <item index="63" class="java.lang.String" itemvalue="requests" />
            <item index="64" class="java.lang.String" itemvalue="websocket-client" />
            <item index="65" class="java.lang.String" itemvalue="bcrypt" />
            <item index="66" class="java.lang.String" itemvalue="python-dotenv" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="app.core.*" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>